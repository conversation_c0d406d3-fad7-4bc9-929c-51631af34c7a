<asset:javascript src="client/clientServiceDataTable.js" />
<style>
.hidden-card-datatable {
    display: none;
    position: absolute;
    top: -24px;
    right: -279px;
    z-index: 1;
}
.hidden-card-datatable:hover {
    display: block;
}
.avatars-datatable:hover + .hidden-card-datatable
{
    display: block;
}
#opportunity-calendar #calendardateStart:before,#opportunity-calendar #calendardateEnd:before,
#opportunity-calendar #calendardateParts:before,#opportunity-calendar #calendarnextTask:before{
    font-size: 28px;
    margin-left: -5px;
    line-height: normal;
}
#opportunity-calendar #calendardateStart div, #opportunity-calendar #calendardateEnd div
, #opportunity-calendar #calendardateParts div, #opportunity-calendar #calendarnextTask div{
    position: relative;
    width: 18px;
    height: 13.4px;
    bottom: 22px;
    font-size: 13px;
    margin-bottom: -18px;
    align-items: center;
    display: flex;
    justify-content: center;
}

</style>
<div class="p-0">
    <div class="d-flex flex-row mb-3">
        <h4 class="text-uppercase mb-5"><g:message code="opportunities.service"/></h4>
    </div>

    <div id="servicesTable"></div>
</div>

<script>
    $(document).ready(function () {
        window.clientServiceDataTable = new ClientServiceDataTable({
            uuid: 'clientServiceDataTableTab',
            divId: 'servicesTable',
            clientId: ${clientId}
        });
    });

</script>