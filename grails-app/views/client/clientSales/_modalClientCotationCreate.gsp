<asset:javascript src="client/clientCotationsMultipleStepForm.js"/>
<style>
.form-step-slider {
    width: 2em;
    height: 3px;
    background-color: var(--bs-primary-bg-subtle);
    border-radius: 10px;
}

.form-step-slider.active {
    background-color: var(--bs-primary);
}

.modal-xl {
    max-width: 1300px;
}
</style>

<div class="modal-dialog modal-xl modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header pb-5">
            <h2 class="modal-title fw-bold">
                <g:message code="cotation.create"/>
            </h2>
            <button type="button" class="btn-close opacity-100" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
            <div class="d-flex flex-row"></div>

            <div id="step-display" class="d-flex flex-row form-multiple-step mb-4">
                <div id="step-slider-1" class="form-step-slider me-2 active ms-0 my-0"></div>

                <div id="step-slider-2" class="form-step-slider me-2 my-0"></div>
            </div>

            <div id="form-multiple-step-containers">
                <div id="view-step-1">
                    <div class="d-flex justify-content-between flex-row fw-bold mb-3 f-16 line-height-19">
                        <span>1. <g:message code="select.opportunities"/></span>
                        <button class="btn btn-link" type="button" onclick="TractionModal.show({
                            url: '/opportunity/modalCreateOpportunity',
                            data: {
                                clientId: ${clientId},
                                returnMethod: 'clientCotationCreate'
                            },
                            bigLoader: true
                        })">
                            <u><g:message code="cotation.create.opportunity"/></u>
                        </button>
                    </div>
                    <g:each var="opportunity" in="${opportunities}">
                        <opportunityTagLib:card opportunity="${opportunity}" selectable="true"
                                                selected="${opportunity.id == selectedOpp?.id}"/>
                    </g:each>
                </div>


                <div id="view-step-2">
                    <div class="d-flex flex-row justify-content-between align-items-center mb-3 fw-bold f-16 line-height-19">
                        <span>2. <g:message code="select.client.vehicle.to.trade"/></span>
                        <button class="btn btn-link" type="button" onclick="TractionModal.show({
                            url: '/vehicle/create',
                            data: {
                                client: ${clientId},
                                returnMethod: 'clientCotationCreate',
                                modal: true
                            },
                            bigLoader: true
                        })">
                            <u><g:message code="cotation.create.trade.vehicle"/></u>
                        </button>
                    </div>

                    <g:each var="vehicle" in="${vehicles}">
                        <vehicleTagLib:tradeCard vehicle="${vehicle}" selectable="true" selected="${vehicle.id == selectedVehicle?.id}"/>
                    </g:each>
                </div>
            </div>
        </div>

        <div class="modal-footer pt-5">
            <div class="d-flex m-0 gap-2">
                <button id="back-step-btn" class="btn btn-outline-primary rounded-5 fw-bold f-16 line-height-19" style="padding: 10px 16px;"><i class="fa-solid fa-chevron-left ms-1 lh-sm me-2"></i><g:message code="back.step"/></button>
                <button id="next-step-btn" class="btn btn-primary rounded-5 fw-bold f-16 line-height-19" style="padding: 10px 16px;"><g:message code="next.step"/><i class="fa-solid fa-chevron-right ms-1 lh-sm ms-2"></i></button>
                <button id="submit-btn" class="btn btn-primary rounded-5 fw-bold"><g:message code="create"/></button>
            </div>
        </div>
    </div>
</div>

<script>

    $(document).ready(function () {
        if (!window.cotationMultiStepForm) {
            window.cotationMultiStepForm = new CotationMultipleStepForm(${clientId});
        } else {
            window.cotationMultiStepForm.reinit();
        }

        <g:if test="${selectedOpp}">
        window.cotationMultiStepForm.formData.opportunities.push(${selectedOpp.id});
        window.cotationMultiStepForm.handleCheckedStep();
        </g:if>
        <g:if test="${selectedVehicle}">
        window.cotationMultiStepForm.formData.vehicles.push(${selectedVehicle.id});
        window.cotationMultiStepForm.handleCheckedStep();
        </g:if>
        $('.opportunity-selection').on('change', function () {
            if ($(this).is(':checked')) {
                window.cotationMultiStepForm.formData.opportunities.push($(this).val());
                window.cotationMultiStepForm.handleCheckedStep();
            } else {
                console.log('Checkbox with ID ' + $(this).attr('id') + ' is unchecked');
                window.cotationMultiStepForm.formData.opportunities = window.cotationMultiStepForm.formData.opportunities.filter(id => id !== $(this).val());
                window.cotationMultiStepForm.handleCheckedStep();
            }
        });

        $('.vehicle-selection').on('change', function () {
            if ($(this).is(':checked')) {
                window.cotationMultiStepForm.formData.vehicles.push($(this).val());
                window.cotationMultiStepForm.handleCheckedStep();
            } else {
                console.log('Checkbox with ID ' + $(this).attr('id') + ' is unchecked');
                window.cotationMultiStepForm.formData.vehicles = window.cotationMultiStepForm.formData.vehicles.filter(id => id !== $(this).val());
                window.cotationMultiStepForm.handleCheckedStep();
            }
        });

        // Bouton pour l'étape suivante
        $('#next-step-btn').on('click', () => {
            window.cotationMultiStepForm.nextStep();
        });

        // Bouton pour l'étape précédente
        $('#back-step-btn').on('click', () => {
            window.cotationMultiStepForm.previousStep();
        });

        $('#submit-btn').on('click', () => window.cotationMultiStepForm.submit());

    });

</script>
