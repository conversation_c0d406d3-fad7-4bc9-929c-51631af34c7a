<g:if test="${partsInvoice.size() == 0}">
    <div class="d-flex justify-content-center align-items-center h-100">
        <span class="text-body-tertiary f-14"><g:message code="client.partsInvoice.empty"></g:message></span>
    </div>
</g:if>
<g:each in="${partsInvoice}" var="partInvoice">
    <div class="d-flex align-items-center justify-content-between flex-wrap mb-3 line-height-19">
        <a class="text-decoration-underline" target="_blank"
           href="${g.createLink(controller: 'lautopakPartReservation', action: 'index')}?roID=${partInvoice.roID}">${partInvoice.roID}</a>
        <i class="f-16 ${partInvoice.lautopakPartReservationStatus.icon}" style="color: ${partInvoice.lautopakPartReservationStatus.iconColor}"></i>
    </div>
</g:each>

<script>
    $('#clientParts-container .totalPartsCount').html('${partsInvoice.size()}');
</script>
