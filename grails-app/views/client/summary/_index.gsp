<div class="d-flex align-items-center justify-content-between mb-5">
    <h4 class="mb-0 text-uppercase"><g:message code="client.summary"/></h4>
</div>

<div class="d-flex align-items-start" style="height: 750px;">
    <div class="d-flex flex-column h-100" style="width: 75%">
        <div class="row col-12" style="height: 160px;margin-bottom: 10px">
            <div class="col-3 p-0 h-100">
                <div class="card flex-grow-1 me-2 h-100">
                    <div class="border-bottom mb-2 py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center h-19">
                            <h6 class="m-0"><g:message code="client.call.notes"/></h6>
                            <a href="javascript:void(0);"  onclick="ClientIndex.router.navigate('callNotes');">
                                <i class="fa-regular fa-arrow-up-right-from-square f-12 text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div id="clientCallNotes" class="py-2 px-3 overflow-y-scroll h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
            <div class="col-3 p-0 h-100">
                <div class="card flex-grow-1 me-2 h-100">
                    <div class="border-bottom mb-2 py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center h-19">
                            <h6 class="m-0"><g:message code="status.client.tasktodo"/></h6>
                            <a href="javascript:void(0);" onclick="ClientIndex.router.navigate('tasks');">
                                <i class="fa-regular fa-arrow-up-right-from-square f-12 text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div id="clientTask" class="py-2 px-3 overflow-y-scroll h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
            <div id="clientWorkOrders-container" class="col-3 p-0 h-100">
                <div class="card flex-grow-1 me-2 h-100">
                    <div class="border-bottom mb-2 py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center h-19">
                            <h6 class="m-0"><g:message code="client.workOrders"/> (<span class="totalWorkOrdersCount">0</span>)</h6>
                            <a href="javascript:void(0);" onclick="ClientIndex.router.navigate('workOrders');">
                                <i class="fa-regular fa-arrow-up-right-from-square f-12 text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div id="clientWorkOrders" class="py-2 px-3 overflow-y-scroll h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
            <div id="clientParts-container" class="col-3 p-0 h-100">
                <div class="card flex-grow-1 h-100">
                    <div class="border-bottom mb-2 py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center h-19">
                            <h6 class="m-0"><g:message code="client.parts"/> (<span class="totalPartsCount">0</span>)</h6>
                            <a href="${g.createLink(controller: 'lautopakPartReservation', action: 'index', params: [clientId: client.id])}" target="_blank">
                                <i class="fa-regular fa-arrow-up-right-from-square f-12 text-primary"></i>
                            </a>
                        </div>
                    </div>

                    <div id="clientParts" class="py-2 px-3 overflow-y-scroll h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row col-12" style="height: 580px;">
            <div id="clientCallsHistory-container" class="col-4 p-0 h-100">
                <div class="card me-2 h-100">
                    <div class="border-bottom p-3">
                        <h5 class="text-uppercase m-0">
                            <g:message code="client.call.history"/>
                        </h5>
                    </div>
                    <div class="d-flex justify-content-between align-items-center border-bottom p-3">
                        <div class="d-flex">
                            <label class="me-1"><g:message code="client.nb.call.today"/>:</label>
                            <span id="callsTodayCount" class="fw-bold">0</span>
                        </div>
                        <i class="fa-regular fa-history cursor text-primary" onclick="ClientIndex.router.navigate('calls');"></i>
                    </div>

                    <div id="clientCallsHistory" class="d-flex flex-column overflow-y-scroll flex-grow-1 h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
            <div id="clientSales-container" class="col-4 p-0 h-100">
                <div class="card pt-3 me-2 h-100">
                    <h5 class="text-uppercase px-3 mb-0"><g:message code="client.sales"/></h5>
                    <ul class="nav nav-tabs md-tabs nav-pills border-bottom d-flex  px-3 flex-wrap mb-3">

                        <li class="nav-item me-3">
                            <span class="nav-link cursor p-0 active" id="opportunity-tab" onclick="showTab('opportunity')">
                                <g:message code="client.opportunity"/>
                            </span>
                        </li>

                        <li class="nav-item">
                            <span class="nav-link p-0 cursor " id="sale-tab" onclick="showTab('sale')">
                                <g:message code="client.purchase"/>
                            </span>
                        </li>

                    </ul>
                    <div id="clientSales" class="tab-content overflow-y-scroll px-3 h-100 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
            <div id="clientWorkflow-container" class="col-4 p-0 h-100">
                <div class="card h-100">
                    <div class="d-flex flex-column border-bottom mb-2 p-3">
                        <h5 class="text-uppercase"><g:message code="client.workflow"/></h5>
                        <select name="department"
                                class="form-select"
                                id="department-multiselect"
                                onchange="filterOpportunitiesByDepartment()">
                            <option value="all"><g:message code="all.departments"/></option>
                        </select>
                    </div>

                    <div id="clientWorkflow" class="d-flex flex-column align-items-center flex-grow-1 overflow-y-scroll gap-2 py-2 px-3 divLoader">
                        <div class="loader"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="h-100" style="width: 25%">
        <div id="clientEvents-container" class="card flex-grow-1 flex-nowrap" style="height: 100%;">
            <div class="px-4 py-3 border-bottom d-flex flex-column mb-2">
                <div class="d-flex justify-content-between ">
                    <h5><g:message code="client.event.upcoming"/></h5>
                    <a class="d-flex align-items-center cursor text-primary" onclick="toggleCalendar()">
                        <i class="fa-solid fa-calendar-days me-2"></i>
                        <span class="text-decoration-underline" id="toggle-text">
                            <g:message code="Show"/>
                        </span>
                    </a>
                </div>
                <select name="department"
                        class="form-select"
                        id="department-select"
                        onchange="filterEvents()">
                    <option value="all"><g:message code="all.departments"/></option>à
                </select>
            </div>

            <div id="calendar" class="mb-3" style="display: none;"></div>
            <hr id="hr" class="p-0 m-0" style="display: none; border-color: #CCCCCC"/>

            <div id="clientEvents" class="d-flex flex-column overflow-y-scroll flex-grow-1 divLoader" style="height: 578px">
                <div class="loader"></div>
            </div>
        </div>
    </div>
</div>

<style>
.card-workflow {
    margin: 0 !important;
    width: -webkit-fill-available;
}
</style>
<script>
    tabInit("/client/clientCallNotes", "#clientCallNotes");
    tabInit("/client/clientTask", "#clientTask");
    tabInit("/client/clientWorkOrders", "#clientWorkOrders");
    tabInit("/client/clientParts", "#clientParts");
    tabInit("/client/clientCallsHistory", "#clientCallsHistory");
    tabInit("/client/clientSales", "#clientSales");
    tabInit("/client/clientWorkflow", "#clientWorkflow");
    tabInit("/client/clientEvents", "#clientEvents");

    function tabInit(url, divId) {
        $.get({
            url: tractionWebRoot + url + "?clientId=${client.id}",
            success: function (data) {
                $(divId).html(data);
            }
        });
    }
    function showTab(tabId) {
        // Hide all tab panes
        document.querySelectorAll('.tab-pane').forEach(function(pane) {
            pane.classList.remove('active');
        });

        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(function(link) {
            link.classList.remove('active');
        });

        // Show the selected tab pane
        document.getElementById(tabId).classList.add('active');

        // Add active class to the selected nav link
        document.getElementById(tabId + '-tab').classList.add('active');
    }
</script>